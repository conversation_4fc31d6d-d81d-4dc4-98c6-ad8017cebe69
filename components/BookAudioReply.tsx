'use client'

import { useState } from 'react'
import Image from 'next/image'
import { AudioPlayer } from './AudioPlayer'
import { AudioRecorder } from './AudioRecorder'
import { ReactionSystem } from './ReactionSystem'
import { User, Mic, Trash2 } from 'lucide-react'
import { Day1Badge } from './Day1Badge'
import { formatTimeAgo } from '@/lib/utils/date'

interface BookAudioReply {
  id: string
  user_id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  parent_reply_id?: string | null
  created_at: string
  reaction_counts?: Record<string, number>
  userReaction?: string | null
  user?: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
  }
  children?: BookAudioReply[]
  level?: number
}

interface BookAudioReplyProps {
  reply: BookAudioReply & { children?: BookAudioReply[]; level?: number }
  bookId: string
  currentUserId?: string
  onReply?: (parentReplyId: string) => void
  onNestedReply?: (parentReplyId: string, audioBlob: Blob, duration: number) => Promise<void>
  onDelete?: (replyId: string) => void
  level?: number // For nested replies
  maxNestingLevel?: number
}

export function BookAudioReply({
  reply,
  bookId,
  currentUserId,
  onReply,
  onNestedReply,
  onDelete,
  level = 0,
  maxNestingLevel = 3
}: BookAudioReplyProps) {
  const [reactions, setReactions] = useState(reply.reaction_counts || {})
  const [userReaction, setUserReaction] = useState(reply.userReaction)
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    return `${Math.floor(diffInSeconds / 86400)}d ago`
  }

  const handleReply = () => {
    if (currentLevel < maxNestingLevel) {
      setShowReplyRecorder(true)
    } else {
      onReply?.(reply.id)
    }
  }

  const handleReactionUpdate = (newReactions: Record<string, number>, newUserReaction: string | null) => {
    setReactions(newReactions)
    setUserReaction(newUserReaction)
  }

  const handleAudioReply = async (audioBlob: Blob, duration: number) => {
    try {
      await onNestedReply?.(reply.id, audioBlob, duration)
      setShowReplyRecorder(false)
    } catch (error) {
      console.error('Error creating nested reply:', error)
      alert('Failed to create reply. Please try again.')
    }
  }

  // Add engagement indicators for deep threading
  const getEngagementIndicator = (level: number) => {
    if (level >= 3) return '🔥'
    if (level >= 2) return '💬'
    return ''
  }

  const currentLevel = reply.level || level || 0

  return (
    <div className={`${currentLevel > 0 ? 'ml-8 pl-4 border-l-4 border-purple-300 mt-3 bg-purple-25' : 'mt-4'} relative`}>
      {/* DEBUG: Show level indicator */}
      {currentLevel > 0 && (
        <>
          <div className="absolute left-[-4px] top-0 w-1 h-8 bg-purple-400"></div>
          <div className="absolute left-[-4px] top-8 w-4 h-0.5 bg-purple-300"></div>
        </>
      )}

      <div className={`${
        currentLevel > 0 ? 'bg-gray-50' : 'bg-blue-50'
      } rounded-lg p-3 sm:p-4 shadow-sm border transition-all duration-200 ${
        currentLevel > 0 ? 'border-gray-200' : 'border-blue-200'
      } hover:shadow-md`}>

        {/* Reply label for nested replies */}
        {currentLevel > 0 && (
          <div className="mb-2">
            <span className="text-xs text-purple-600 font-bold uppercase tracking-wide bg-purple-100 px-2 py-1 rounded">
              ↳ REPLY {getEngagementIndicator(currentLevel)}
            </span>
          </div>
        )}

        {/* User Info - Smaller for replies */}
        <div className="flex items-center gap-2 mb-2 sm:mb-3">
          {reply.user?.profile_picture_url || reply.user?.avatar ? (
            <div>
              <Image
                src={reply.user.profile_picture_url || reply.user.avatar || ''}
                alt={reply.user?.name || 'Anonymous'}
                width={level > 0 ? 24 : 32}
                height={level > 0 ? 24 : 32}
                className={`${level > 0 ? 'w-6 h-6' : 'w-6 h-6 sm:w-8 sm:h-8'} rounded-full object-cover`}
              />
            </div>
          ) : (
            <div className={`${level > 0 ? 'w-6 h-6' : 'w-6 h-6 sm:w-8 sm:h-8'} rounded-full bg-gray-400 flex items-center justify-center`}>
              <User className={`${level > 0 ? 'w-3 h-3' : 'w-3 h-3 sm:w-4 sm:h-4'} text-white`} />
            </div>
          )}

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 sm:gap-2">
              <p className={`font-medium truncate ${
                currentLevel > 0 ? 'text-xs text-gray-600' : 'text-xs sm:text-sm text-gray-900'
              }`}>
                {reply.user?.name || 'Anonymous'}
              </p>
              {reply.user?.has_day1_badge && currentLevel === 0 && (
                <Day1Badge
                  signupNumber={reply.user.signup_number}
                  size="sm"
                  showTooltip={false}
                />
              )}
            </div>
            <p className={`text-xs ${currentLevel > 0 ? 'text-gray-400' : 'text-gray-500'}`}>
              {formatTimeAgo(reply.created_at)}
            </p>
          </div>

          {/* Delete button for reply owner */}
          {currentUserId && (currentUserId === reply.user_id || currentUserId === reply.user?.id) && onDelete && (
            <button
              onClick={() => onDelete(reply.id)}
              className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-full transition-colors"
              title="Delete this reply"
            >
              <Trash2 className="w-3 h-3" />
            </button>
          )}


        </div>

        {/* Audio Player - Different styling for replies */}
        <div className={`mb-3 p-2 rounded-lg border ${
          currentLevel > 0
            ? 'bg-gradient-to-r from-gray-100 to-purple-50 border-gray-200'
            : 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100'
        }`}>
          <div className="flex items-center gap-2 mb-2">
            <Mic className={`h-3 w-3 ${currentLevel > 0 ? 'text-purple-600' : 'text-blue-600'}`} />
            <span className={`text-xs font-medium ${
              currentLevel > 0 ? 'text-purple-700' : 'text-blue-700'
            }`}>
              Audio Reply • {reply.duration_seconds.toFixed(1)}s
            </span>
          </div>
          <AudioPlayer
            audioUrl={reply.audio_url}
            duration={reply.duration_seconds}
            className="w-full"
          />
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2 sm:gap-4">
          {/* Reaction System */}
          <ReactionSystem
            contentType="book_audio_reply"
            contentId={reply.id}
            bookId={bookId}
            currentUserId={currentUserId}
            initialReactions={reactions}
            userReaction={userReaction}
            onReactionUpdate={handleReactionUpdate}
          />

          {currentLevel < maxNestingLevel && (
            <button
              onClick={handleReply}
              disabled={!currentUserId}
              className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-3 py-2 rounded-lg transition-all text-blue-600 hover:bg-blue-50 min-h-[44px] ${
                !currentUserId ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              <Mic className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="text-xs sm:text-sm font-medium">Reply</span>
            </button>
          )}
        </div>

        {/* Nested Reply Recorder */}
        {showReplyRecorder && (
          <div className="mt-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
            <div className="flex items-center gap-2 mb-3">
              <Mic className="h-4 w-4 text-purple-600" />
              <p className="text-sm font-medium text-purple-700">Reply to {reply.user?.name}</p>
            </div>
            <AudioRecorder
              onRecordingComplete={handleAudioReply}
              onCancel={() => setShowReplyRecorder(false)}
              maxDuration={9}
              className="w-full"
            />
          </div>
        )}
      </div>

      {/* Render nested replies */}
      {reply.children && reply.children.length > 0 && (
        <div className="mt-2">
          {reply.children.map((childReply) => (
            <BookAudioReply
              key={childReply.id}
              reply={childReply}
              bookId={bookId}
              currentUserId={currentUserId}
              onNestedReply={onNestedReply}
              onDelete={onDelete}
              level={currentLevel + 1}
              maxNestingLevel={maxNestingLevel}
            />
          ))}
        </div>
      )}
    </div>
  )
}
