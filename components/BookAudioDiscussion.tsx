'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { AudioRecorder } from '@/components/AudioRecorder'
import { AudioPlayer } from '@/components/AudioPlayer'
import { ReactionSystem } from '@/components/ReactionSystem'
import { BookAudioReply } from '@/components/BookAudioReply'
import { Day1Badge } from '@/components/Day1Badge'
import { Heart, MessageCircle, X, Mic, Users, User } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Image from 'next/image'

interface BookAudioPost {
  id: string
  audio_url: string
  description?: string
  duration_seconds: number
  chapter_position: number
  page_context?: string
  love_count: number
  reply_count: number
  created_at: string
  reaction_counts?: Record<string, number>
  userReaction?: string | null
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
}

interface BookAudioReply {
  id: string
  audio_url: string
  duration_seconds: number
  love_count: number
  parent_reply_id?: string | null
  created_at: string
  reaction_counts?: Record<string, number>
  userReaction?: string | null
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
    has_day1_badge?: boolean
    signup_number?: number
    badge_tier?: string
  }
  children?: BookAudioReply[]
  level?: number
}

interface BookAudioDiscussionProps {
  bookId: string
  chapterId: string
  chapterTitle: string
  currentUserId?: string
  onClose: () => void
  onDiscussionAdded?: () => void
}

export function BookAudioDiscussion({
  bookId,
  chapterId,
  chapterTitle,
  currentUserId,
  onClose,
  onDiscussionAdded
}: BookAudioDiscussionProps) {
  const [posts, setPosts] = useState<BookAudioPost[]>([])
  const [loading, setLoading] = useState(true)
  const [showRecorder, setShowRecorder] = useState(false)
  const [selectedPost, setSelectedPost] = useState<BookAudioPost | null>(null)
  const [allReplies, setAllReplies] = useState<BookAudioReply[]>([])
  const [showReplyRecorder, setShowReplyRecorder] = useState(false)
  const [recordingDescription, setRecordingDescription] = useState('')

  const supabase = createSupabaseClient()

  useEffect(() => {
    loadPosts()
  }, [bookId, chapterId])

  const loadPosts = async () => {
    try {
      const response = await fetch(`/api/books/${bookId}/audio/posts?chapterId=${chapterId}`)
      if (response.ok) {
        const data = await response.json()
        setPosts(data.posts || [])

        // Load all replies for all posts
        if (data.posts && data.posts.length > 0) {
          await loadAllReplies(data.posts)
        }
      }
    } catch (error) {
      console.error('Error loading posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadAllReplies = async (posts: BookAudioPost[]) => {
    try {
      const allRepliesPromises = posts.map(post =>
        fetch(`/api/books/${bookId}/audio/replies?postId=${post.id}`)
          .then(response => response.ok ? response.json() : { replies: [] })
          .then(data => data.replies || [])
      )

      const repliesArrays = await Promise.all(allRepliesPromises)
      const flatReplies = repliesArrays.flat()
      setAllReplies(flatReplies)
    } catch (error) {
      console.error('Error loading all replies:', error)
    }
  }

  const loadReplies = async (postId: string) => {
    try {
      const response = await fetch(`/api/books/${bookId}/audio/replies?postId=${postId}`)
      if (response.ok) {
        const data = await response.json()
        setAllReplies(prev => {
          // Update replies for this specific post
          const otherReplies = prev.filter(reply =>
            !data.replies.some((newReply: BookAudioReply) => newReply.id === reply.id)
          )
          return [...otherReplies, ...(data.replies || [])]
        })
      }
    } catch (error) {
      console.error('Error loading replies:', error)
    }
  }

  const handleRecordingComplete = async (audioBlob: Blob, duration: number) => {
    try {
      // Upload audio file
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'post', duration })
      })

      if (!uploadResponse.ok) throw new Error('Upload failed')

      const { uploadUrl, publicUrl, key } = await uploadResponse.json()

      // Upload the audio file
      await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: { 'Content-Type': 'audio/webm' }
      })

      // Create the book audio post
      const createResponse = await fetch(`/api/books/${bookId}/audio/posts`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chapterId,
          audioUrl: publicUrl,
          audioKey: key,
          duration,
          description: recordingDescription.trim() || null,
          chapterPosition: 0, // Could be enhanced to track actual position
          pageContext: chapterTitle
        })
      })

      if (createResponse.ok) {
        const { post } = await createResponse.json()
        setPosts(prev => [post, ...prev])
        setShowRecorder(false)
        setRecordingDescription('')
        // Update the parent's discussion count
        onDiscussionAdded?.()
      }
    } catch (error) {
      console.error('Error creating audio post:', error)
      alert('Failed to create audio post. Please try again.')
    }
  }

  const handleReplyComplete = async (audioBlob: Blob, duration: number, parentReplyId?: string) => {
    if (!selectedPost) return

    try {
      // Upload audio file
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'reply', duration })
      })

      if (!uploadResponse.ok) throw new Error('Upload failed')

      const { uploadUrl, publicUrl, key } = await uploadResponse.json()

      // Upload the audio file
      await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: { 'Content-Type': 'audio/webm' }
      })

      // Create the reply
      const createResponse = await fetch(`/api/books/${bookId}/audio/replies`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          postId: selectedPost.id,
          parentReplyId,
          audioUrl: publicUrl,
          audioKey: key,
          duration
        })
      })

      if (createResponse.ok) {
        const { reply } = await createResponse.json()
        setAllReplies(prev => [...prev, reply])
        setShowReplyRecorder(false)

        // Update reply count in posts
        setPosts(prev => prev.map(post =>
          post.id === selectedPost.id
            ? { ...post, reply_count: post.reply_count + 1 }
            : post
        ))
      }
    } catch (error) {
      console.error('Error creating reply:', error)
      alert('Failed to create reply. Please try again.')
    }
  }

  const handleNestedReply = async (parentReplyId: string, audioBlob: Blob, duration: number) => {
    await handleCreateReply(audioBlob, duration, parentReplyId)
  }

  const handlePostClick = (post: BookAudioPost) => {
    setSelectedPost(post)
    loadReplies(post.id)
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  // Organize replies into a hierarchical structure
  const organizeReplies = (replies: BookAudioReply[]) => {
    console.log('DEBUG: Raw replies data:', replies.map(r => ({
      id: r.id,
      parent_reply_id: r.parent_reply_id,
      user: r.user?.name
    })))

    const replyMap = new Map<string, BookAudioReply & { level: number; children: (BookAudioReply & { level: number })[] }>()
    const rootReplies: (BookAudioReply & { level: number; children: (BookAudioReply & { level: number })[] })[] = []

    // First pass: create map and identify root replies
    replies.forEach(reply => {
      const replyWithLevel = { ...reply, level: 0, children: [] }
      replyMap.set(reply.id, replyWithLevel)

      if (!reply.parent_reply_id) {
        rootReplies.push(replyWithLevel)
      }
    })

    // Second pass: organize into hierarchy
    replies.forEach(reply => {
      if (reply.parent_reply_id) {
        const parent = replyMap.get(reply.parent_reply_id)
        const child = replyMap.get(reply.id)
        if (parent && child) {
          child.level = parent.level + 1
          parent.children.push(child)
        }
      }
    })

    console.log('DEBUG: Organized replies:', rootReplies.map(r => ({
      id: r.id,
      level: r.level,
      children: r.children.length,
      user: r.user?.name
    })))

    // Return hierarchical structure instead of flattening
    return rootReplies
  }

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-center">Loading discussions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-hidden flex flex-col">
        {/* Enhanced Header */}
        <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-1">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                  <Mic className="h-4 w-4 text-white" />
                </div>
                <h2 className="text-lg font-bold text-gray-900">Audio Discussions</h2>
              </div>
              <p className="text-sm text-gray-600 ml-11">
                <span className="font-medium">{chapterTitle}</span>
                {posts.length > 0 && (
                  <span className="ml-2 text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                    {posts.length} {posts.length === 1 ? 'discussion' : 'discussions'}
                  </span>
                )}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="ml-2 flex-shrink-0 min-h-[44px] min-w-[44px] p-2 hover:bg-white/50"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {selectedPost ? (
            /* Reply View */
            <div className="p-3 sm:p-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedPost(null)}
                className="mb-3 sm:mb-4 min-h-[44px] px-3 py-2"
              >
                ← Back to discussions
              </Button>
              
              {/* Original Post */}
              <div className="bg-blue-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
                <div className="flex items-center gap-2 sm:gap-3 mb-2">
                  {/* User Profile Picture */}
                  {selectedPost.user.profile_picture_url || selectedPost.user.avatar ? (
                    <Image
                      src={selectedPost.user.profile_picture_url || selectedPost.user.avatar || ''}
                      alt={selectedPost.user.name}
                      width={32}
                      height={32}
                      className="w-6 h-6 sm:w-8 sm:h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-6 h-6 sm:w-8 sm:h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs sm:text-sm font-medium flex-shrink-0">
                      <User className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-1 sm:gap-2">
                      <p className="font-medium text-xs sm:text-sm text-gray-900 truncate">{selectedPost.user.name}</p>
                      {selectedPost.user.has_day1_badge && (
                        <Day1Badge
                          signupNumber={selectedPost.user.signup_number}
                          size="sm"
                          showTooltip={false}
                        />
                      )}
                    </div>
                    <p className="text-xs text-gray-500">{formatTimeAgo(selectedPost.created_at)}</p>
                  </div>
                </div>
                
                {selectedPost.description && (
                  <p className="text-sm text-gray-700 mb-2">{selectedPost.description}</p>
                )}
                
                <AudioPlayer
                  audioUrl={selectedPost.audio_url}
                  duration={selectedPost.duration_seconds}
                  className="mb-3"
                />

                <div className="flex items-center gap-4">
                  <ReactionSystem
                    contentType="book_audio_post"
                    contentId={selectedPost.id}
                    bookId={bookId}
                    currentUserId={currentUserId}
                    initialReactions={selectedPost.reaction_counts || {}}
                    userReaction={selectedPost.userReaction}
                  />
                  <span className="flex items-center gap-1 text-xs text-gray-500">
                    <Mic className="h-3 w-3" />
                    {selectedPost.reply_count} replies
                  </span>
                </div>
              </div>

              {/* Replies */}
              <div className="space-y-3">
                {organizeReplies(allReplies).map((reply) => (
                  <BookAudioReply
                    key={reply.id}
                    reply={reply}
                    bookId={bookId}
                    currentUserId={currentUserId}
                    onNestedReply={handleNestedReply}
                    level={0}
                    maxNestingLevel={3}
                  />
                ))}
              </div>

              {/* Reply Recorder */}
              {currentUserId && (
                <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200">
                  {showReplyRecorder ? (
                    <div className="bg-blue-50 rounded-lg p-3 sm:p-4">
                      <h4 className="text-xs sm:text-sm font-medium mb-2 sm:mb-3">🎤 Record Reply</h4>
                      <AudioRecorder
                        maxDuration={9}
                        onRecordingComplete={handleReplyComplete}
                        onCancel={() => setShowReplyRecorder(false)}
                        className="w-full"
                      />
                    </div>
                  ) : (
                    <Button
                      onClick={() => setShowReplyRecorder(true)}
                      className="w-full min-h-[44px] text-sm"
                      variant="outline"
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Record Reply
                    </Button>
                  )}
                </div>
              )}
            </div>
          ) : (
            /* Posts List View */
            <div className="p-3 sm:p-4">
              {/* Create New Post */}
              {currentUserId && (
                <div className="mb-4 sm:mb-6">
                  {showRecorder ? (
                    <div className="bg-blue-50 rounded-lg p-3 sm:p-4">
                      <h3 className="text-xs sm:text-sm font-medium mb-2 sm:mb-3">🎤 Record Audio Discussion</h3>
                      <input
                        type="text"
                        placeholder="Optional description (50 chars max)"
                        value={recordingDescription}
                        onChange={(e) => setRecordingDescription(e.target.value.slice(0, 50))}
                        className="w-full p-2 sm:p-3 border border-gray-300 rounded mb-2 sm:mb-3 text-xs sm:text-sm min-h-[44px]"
                      />
                      <AudioRecorder
                        maxDuration={9}
                        onRecordingComplete={handleRecordingComplete}
                        onCancel={() => {
                          setShowRecorder(false)
                          setRecordingDescription('')
                        }}
                        className="w-full"
                      />
                    </div>
                  ) : (
                    <Button
                      onClick={() => setShowRecorder(true)}
                      className="w-full mb-3 sm:mb-4 min-h-[44px] text-sm"
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Start Audio Discussion
                    </Button>
                  )}
                </div>
              )}

              {/* Enhanced Posts List with Better UX */}
              {posts.length === 0 ? (
                <div className="text-center py-12 text-gray-500">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center">
                    <Mic className="h-8 w-8 text-purple-500" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-700 mb-2">No discussions yet</h3>
                  <p className="text-sm text-gray-500 mb-4">Start the conversation about this chapter</p>
                  {currentUserId && (
                    <Button
                      onClick={() => setShowRecorder(true)}
                      className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      <Mic className="h-4 w-4 mr-2" />
                      Start First Discussion
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {posts.map((post, index) => {
                    // Enhanced timestamp formatting
                    const formatFacebookTime = (dateString: string) => {
                      const date = new Date(dateString)
                      const now = new Date()
                      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

                      if (diffInMinutes < 1) return 'Just now'
                      if (diffInMinutes < 60) return `${diffInMinutes}m ago`
                      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
                      if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`

                      return date.toLocaleDateString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
                      })
                    }

                    // Get replies for this specific post - improved logic
                    const postReplies = allReplies.filter(reply => {
                      // This is a simplified approach - in a real app you'd have a post_id field on replies
                      // For now, we'll show replies when the post is expanded
                      return selectedPost?.id === post.id
                    })

                    const isExpanded = selectedPost?.id === post.id
                    const hasReplies = post.reply_count > 0

                    return (
                      <div key={post.id} className="space-y-3">
                        {/* Enhanced Main Audio Post */}
                        <div className={`bg-white rounded-xl p-4 shadow-sm border transition-all duration-200 ${
                          isExpanded ? 'border-purple-200 shadow-md' : 'border-gray-100 hover:border-gray-200 hover:shadow-md'
                        }`}>

                          {/* Post Header */}
                          <div className="flex items-start gap-3 mb-3">
                            {/* Enhanced User Profile Picture */}
                            {post.user.profile_picture_url || post.user.avatar ? (
                              <div className="flex-shrink-0">
                                <Image
                                  src={post.user.profile_picture_url || post.user.avatar || ''}
                                  alt={post.user.name}
                                  width={48}
                                  height={48}
                                  className="w-12 h-12 rounded-full object-cover ring-2 ring-purple-100"
                                />
                              </div>
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center text-white font-medium flex-shrink-0 ring-2 ring-purple-100">
                                <User className="w-6 h-6" />
                              </div>
                            )}

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-semibold text-gray-900 text-base truncate">{post.user.name}</h3>
                                {post.user.has_day1_badge && (
                                  <Day1Badge
                                    signupNumber={post.user.signup_number}
                                    size="sm"
                                    showTooltip={false}
                                  />
                                )}
                                <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full font-medium">
                                  Discussion
                                </span>
                              </div>
                              <p className="text-sm text-gray-500">{formatFacebookTime(post.created_at)}</p>
                            </div>
                          </div>

                          {/* Post Description */}
                          {post.description && (
                            <div className="mb-3 p-3 bg-gray-50 rounded-lg border-l-4 border-purple-300">
                              <p className="text-sm text-gray-700 italic">"{post.description}"</p>
                            </div>
                          )}

                          {/* Enhanced Audio Player */}
                          <div className="mb-4 p-3 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-100">
                            <div className="flex items-center gap-2 mb-2">
                              <Mic className="h-4 w-4 text-purple-600" />
                              <span className="text-sm font-medium text-purple-700">
                                Audio Discussion • {post.duration_seconds.toFixed(1)}s
                              </span>
                            </div>
                            <AudioPlayer
                              audioUrl={post.audio_url}
                              duration={post.duration_seconds}
                              className="w-full"
                            />
                          </div>

                          {/* Enhanced Action Bar */}
                          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                            <div className="flex items-center gap-4">
                              <ReactionSystem
                                contentType="book_audio_post"
                                contentId={post.id}
                                bookId={bookId}
                                currentUserId={currentUserId}
                                initialReactions={post.reaction_counts || {}}
                                userReaction={post.userReaction}
                              />

                              {/* Enhanced Reply Count */}
                              <button
                                onClick={() => handlePostClick(post)}
                                className={`flex items-center gap-1 text-xs transition-colors ${
                                  hasReplies
                                    ? 'text-purple-600 hover:text-purple-700 font-medium'
                                    : 'text-gray-500'
                                }`}
                              >
                                <Mic className="h-3 w-3" />
                                {post.reply_count === 0 ? 'No replies yet' :
                                 post.reply_count === 1 ? '1 reply' :
                                 `${post.reply_count} replies`}
                                {hasReplies && !isExpanded && (
                                  <span className="ml-1 text-xs">→</span>
                                )}
                              </button>
                            </div>

                            {/* Enhanced Reply Button */}
                            {currentUserId && (
                              <Button
                                variant={isExpanded && showReplyRecorder ? "outline" : "ghost"}
                                size="sm"
                                onClick={() => {
                                  if (selectedPost?.id === post.id) {
                                    setShowReplyRecorder(!showReplyRecorder)
                                  } else {
                                    handlePostClick(post)
                                    setShowReplyRecorder(true)
                                  }
                                }}
                                className={`text-xs transition-all ${
                                  isExpanded && showReplyRecorder
                                    ? 'border-red-200 text-red-600 hover:bg-red-50'
                                    : 'text-purple-600 hover:text-purple-700 hover:bg-purple-50'
                                }`}
                              >
                                <Mic className="h-3 w-3 mr-1" />
                                {isExpanded && showReplyRecorder ? 'Cancel Reply' : 'Reply'}
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Enhanced Reply Section */}
                        {isExpanded && (
                          <div className="mt-4 space-y-4">

                            {/* Reply Recorder */}
                            {showReplyRecorder && currentUserId && (
                              <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 border border-purple-200">
                                <div className="flex items-center gap-2 mb-3">
                                  <Mic className="h-4 w-4 text-purple-600" />
                                  <h4 className="text-sm font-semibold text-purple-700">Record Your Reply</h4>
                                </div>
                                <p className="text-xs text-gray-600 mb-3">
                                  Share your thoughts about this discussion (max 9 seconds)
                                </p>
                                <AudioRecorder
                                  onRecordingComplete={(audioBlob, duration) => handleReplyComplete(audioBlob, duration)}
                                  onCancel={() => setShowReplyRecorder(false)}
                                  maxDuration={9}
                                  className="w-full"
                                />
                              </div>
                            )}

                            {/* Replies Section */}
                            {postReplies.length > 0 ? (
                              <div className="space-y-3">
                                <div className="flex items-center gap-2 pb-2 border-b border-gray-100">
                                  <Mic className="h-4 w-4 text-gray-500" />
                                  <h4 className="text-sm font-semibold text-gray-700">
                                    {postReplies.length === 1 ? '1 Reply' : `${postReplies.length} Replies`}
                                  </h4>
                                </div>

                                <div className="space-y-3">
                                  {organizeReplies(postReplies).map((reply) => (
                                    <BookAudioReply
                                      key={reply.id}
                                      reply={reply}
                                      bookId={bookId}
                                      currentUserId={currentUserId}
                                      onNestedReply={handleNestedReply}
                                      level={0}
                                      maxNestingLevel={3}
                                    />
                                  ))}
                                </div>
                              </div>
                            ) : (
                              !showReplyRecorder && (
                                <div className="text-center py-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                                  <Mic className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                                  <p className="text-sm text-gray-500 mb-3">No replies yet</p>
                                  {currentUserId && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => setShowReplyRecorder(true)}
                                      className="text-purple-600 border-purple-200 hover:bg-purple-50"
                                    >
                                      <Mic className="h-3 w-3 mr-1" />
                                      Be the first to reply
                                    </Button>
                                  )}
                                </div>
                              )
                            )}
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
