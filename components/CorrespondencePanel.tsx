'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { MessageThreadModal } from './MessageThreadModal'
import { Plus } from 'lucide-react'
import Link from 'next/link'

interface Correspondence {
  id: string
  type: 'moment' | 'letter' | 'note'
  title: string
  body: string
  data?: any
  read_at?: string
  created_at: string
  sender?: {
    id: string
    name: string
    avatar?: string
  }
}

interface CorrespondencePanelProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  onUnreadCountChange?: (count: number) => void
}

interface Conversation {
  otherUser: {
    id: string
    name: string
    profile_picture_url?: string
    avatar?: string
  }
  lastMessage: {
    id: string
    body: string
    photo_url?: string
    message_type: string
    created_at: string
    sender_id: string
  }
  unreadCount: number
  totalMessages: number
}

export function CorrespondencePanel({ isOpen, onClose, userId, onUnreadCountChange }: CorrespondencePanelProps) {
  const [activeTab, setActiveTab] = useState<'letters' | 'conversations' | 'notes'>('letters')
  const [correspondence, setCorrespondence] = useState<Correspondence[]>([])
  const [conversations, setConversations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [conversationsLoading, setConversationsLoading] = useState(false)
  const [messageThreadOpen, setMessageThreadOpen] = useState(false)
  const [selectedMessageUser, setSelectedMessageUser] = useState<{
    id: string
    name: string
    avatar?: string
  } | null>(null)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (isOpen && userId) {
      if (activeTab === 'conversations') {
        fetchConversations()
      } else {
        fetchCorrespondence()
      }
    }
  }, [isOpen, userId, activeTab])

  const fetchCorrespondence = async () => {
    setLoading(true)
    try {
      // Fetch notifications (moments & notes)
      const { data: notifications } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      // Fetch direct messages (letters)
      const { data: messages } = await supabase
        .from('direct_messages')
        .select(`
          *,
          sender:users!direct_messages_sender_id_fkey(id, name, avatar, profile_picture_url)
        `)
        .eq('recipient_id', userId)
        .order('created_at', { ascending: false })
        .limit(20)

      const formattedCorrespondence: Correspondence[] = [
        // Notifications (moments & notes)
        ...(notifications || []).map(notif => ({
          id: notif.id,
          type: notif.type === 'recommendation' ? 'moment' : notif.type === 'message' ? 'letter' : 'note',
          title: notif.title,
          body: notif.body,
          data: notif.data,
          read_at: notif.read_at,
          created_at: notif.created_at,
          sender: notif.data?.recommender_name ? {
            id: notif.data.recommender_id,
            name: notif.data.recommender_name,
          } : notif.data?.sender_name ? {
            id: notif.data.sender_id,
            name: notif.data.sender_name,
          } : undefined
        })),
        // Direct messages (letters)
        ...(messages || []).map(msg => ({
          id: msg.id,
          type: 'letter' as const,
          title: msg.subject || 'New Message',
          body: msg.body,
          data: {
            sender_id: msg.sender_id,
            photo_url: msg.photo_url,
            message_type: msg.message_type
          },
          read_at: msg.read_at,
          created_at: msg.created_at,
          sender: {
            id: msg.sender.id,
            name: msg.sender.name,
            avatar: msg.sender.profile_picture_url || msg.sender.avatar
          }
        }))
      ]

      setCorrespondence(formattedCorrespondence)
    } catch (error) {
      console.error('Error fetching correspondence:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = async (correspondenceId: string, type: 'moment' | 'letter' | 'note') => {
    try {
      if (type === 'letter') {
        // Mark direct message as read
        await supabase
          .from('direct_messages')
          .update({ read_at: new Date().toISOString() })
          .eq('id', correspondenceId)
      } else {
        // Mark notification as read
        await supabase
          .from('notifications')
          .update({ read_at: new Date().toISOString() })
          .eq('id', correspondenceId)
      }

      // Update local state
      setCorrespondence(prev =>
        prev.map(item =>
          item.id === correspondenceId
            ? { ...item, read_at: new Date().toISOString() }
            : item
        )
      )

      // Notify parent component to update unread count
      if (onUnreadCountChange) {
        const newUnreadCount = correspondence.filter(item =>
          !item.read_at && item.id !== correspondenceId
        ).length
        onUnreadCountChange(newUnreadCount)
      }
    } catch (error) {
      console.error('Error marking as read:', error)
    }
  }

  const fetchConversations = async () => {
    setConversationsLoading(true)
    try {
      // Get all messages involving the current user
      const { data: messages, error } = await supabase
        .from('direct_messages')
        .select(`
          id,
          sender_id,
          recipient_id,
          body,
          photo_url,
          message_type,
          created_at,
          read_at,
          sender:users!sender_id(id, name, profile_picture_url, avatar),
          recipient:users!recipient_id(id, name, profile_picture_url, avatar)
        `)
        .or(`sender_id.eq.${userId},recipient_id.eq.${userId}`)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Group messages by conversation (other user)
      const conversationMap = new Map<string, Conversation>()

      messages?.forEach(message => {
        const isFromMe = message.sender_id === userId
        const otherUser = isFromMe ? message.recipient : message.sender
        const otherUserId = otherUser.id

        if (!conversationMap.has(otherUserId)) {
          conversationMap.set(otherUserId, {
            otherUser,
            lastMessage: message,
            unreadCount: 0,
            totalMessages: 0
          })
        }

        const conversation = conversationMap.get(otherUserId)!
        conversation.totalMessages++

        // Count unread messages (messages from other user that haven't been read)
        if (!isFromMe && !message.read_at) {
          conversation.unreadCount++
        }

        // Update last message if this one is more recent
        if (new Date(message.created_at) > new Date(conversation.lastMessage.created_at)) {
          conversation.lastMessage = message
        }
      })

      setConversations(Array.from(conversationMap.values()))
    } catch (error) {
      console.error('Error fetching conversations:', error)
    } finally {
      setConversationsLoading(false)
    }
  }

  const filteredCorrespondence = correspondence.filter(item => {
    if (activeTab === 'letters') return item.type === 'letter'
    if (activeTab === 'notes') return item.type === 'note'
    return true
  })

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  const handleNotificationClick = (item: Correspondence) => {
    // Mark as read first
    if (!item.read_at) {
      markAsRead(item.id, item.type)
    }

    // Navigate based on notification type
    if (item.type === 'letter' && item.data?.sender_id) {
      // For direct messages, open the message thread modal
      setSelectedMessageUser({
        id: item.data.sender_id,
        name: item.sender?.name || 'Unknown User',
        avatar: item.sender?.avatar
      })
      setMessageThreadOpen(true)
      // Close the correspondence panel to avoid layering issues
      onClose()
    }
    // Add more navigation logic for other types as needed
  }

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/20 z-40"
        onClick={onClose}
      />
      
      {/* Panel */}
      <div className="fixed top-16 right-4 w-96 max-w-[calc(100vw-2rem)] bg-white rounded-2xl shadow-2xl border border-gray-100 z-50 max-h-[80vh] flex flex-col">
        
        {/* Header */}
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-serif text-gray-800">Correspondence</h2>
            <button 
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-xl"
            >
              ×
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex space-x-1 bg-gray-50 rounded-lg p-1">
            {[
              { key: 'letters', label: 'Letters', icon: '💌' },
              { key: 'conversations', label: 'Chats', icon: '💬' },
              { key: 'notes', label: 'Notes', icon: '📝' }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex-1 flex items-center justify-center gap-2 py-2 px-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.key
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'conversations' ? (
            /* Conversations Tab */
            conversationsLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
              </div>
            ) : conversations.length > 0 ? (
              <div className="p-2">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.otherUser.id}
                    onClick={() => {
                      setSelectedMessageUser({
                        id: conversation.otherUser.id,
                        name: conversation.otherUser.name,
                        avatar: conversation.otherUser.profile_picture_url || conversation.otherUser.avatar
                      })
                      setMessageThreadOpen(true)
                    }}
                    className="p-4 m-2 rounded-xl border transition-all hover:shadow-sm cursor-pointer bg-white border-gray-100 hover:bg-gray-50"
                  >
                    <div className="flex items-center gap-3">
                      {/* Avatar */}
                      {conversation.otherUser.profile_picture_url || conversation.otherUser.avatar ? (
                        <img
                          src={conversation.otherUser.profile_picture_url || conversation.otherUser.avatar}
                          alt={conversation.otherUser.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 flex items-center justify-center text-white font-medium">
                          {conversation.otherUser.name.charAt(0).toUpperCase()}
                        </div>
                      )}

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-gray-900 truncate">
                            {conversation.otherUser.name}
                          </h3>
                          <div className="flex items-center gap-2">
                            {conversation.unreadCount > 0 && (
                              <span className="bg-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center">
                                {conversation.unreadCount}
                              </span>
                            )}
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(conversation.lastMessage.created_at)}
                            </span>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <p className={`text-sm truncate flex-1 ${
                            conversation.unreadCount > 0 ? 'font-medium text-gray-900' : 'text-gray-600'
                          }`}>
                            {conversation.lastMessage.sender_id === userId && (
                              <span className="text-gray-500">You: </span>
                            )}
                            {conversation.lastMessage.message_type === 'photo' ? '📷 Photo' :
                             conversation.lastMessage.message_type === 'text_with_photo' ? `📷 ${conversation.lastMessage.body?.substring(0, 30)}...` :
                             conversation.lastMessage.body?.substring(0, 50) + (conversation.lastMessage.body && conversation.lastMessage.body.length > 50 ? '...' : '')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-4">
                  <span className="text-2xl">💬</span>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No conversations yet</h3>
                <p className="text-gray-500 text-sm">Start a conversation by visiting someone's profile and sending them a message</p>
              </div>
            )
          ) : (
            /* Other tabs (moments, letters, notes) */
            loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
              </div>
            ) : filteredCorrespondence.length > 0 ? (
            <div className="p-2">
              {filteredCorrespondence.map((item) => (
                <div
                  key={item.id}
                  className={`p-4 m-2 rounded-xl border transition-all hover:shadow-sm cursor-pointer ${
                    item.read_at
                      ? 'bg-gray-50 border-gray-100 hover:bg-gray-100'
                      : 'bg-blue-50 border-blue-100 hover:bg-blue-100'
                  }`}
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    handleNotificationClick(item)
                  }}
                >
                  <div className="flex items-start gap-3">
                    {/* Avatar or Icon */}
                    <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-blue-100 flex items-center justify-center flex-shrink-0">
                      {item.sender?.avatar ? (
                        <img 
                          src={item.sender.avatar} 
                          alt={item.sender.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <span className="text-lg">
                          {item.type === 'moment' ? '✨' : item.type === 'letter' ? '💌' : '📝'}
                        </span>
                      )}
                    </div>
                    
                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-gray-800 text-sm mb-1">
                        {item.title}
                      </h4>

                      {/* Show photo if it exists */}
                      {item.data?.photo_url && (
                        <div className="mb-2">
                          <img
                            src={item.data.photo_url}
                            alt="Message photo"
                            className="w-full max-w-xs h-32 object-cover rounded-lg border"
                          />
                        </div>
                      )}

                      {/* Show message body if it exists */}
                      {item.body && (
                        <p className="text-gray-600 text-sm leading-relaxed mb-2">
                          {item.body}
                        </p>
                      )}

                      {/* Show message type indicator */}
                      {item.data?.message_type === 'photo' && !item.body && (
                        <p className="text-gray-500 text-xs italic mb-2">
                          📷 Photo message
                        </p>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {formatTimeAgo(item.created_at)}
                        </span>
                        {!item.read_at && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <span className="text-2xl">
                  {activeTab === 'moments' ? '✨' : activeTab === 'letters' ? '💌' : '📝'}
                </span>
              </div>
              <h3 className="font-medium text-gray-800 mb-2">
                No {activeTab} yet
              </h3>
              <p className="text-gray-500 text-sm">
                {activeTab === 'moments' && "Story moments will appear here"}
                {activeTab === 'letters' && "Direct messages will appear here"}
                {activeTab === 'notes' && "System notes will appear here"}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Message Thread Modal */}
      {selectedMessageUser && (
        <MessageThreadModal
          isOpen={messageThreadOpen}
          onClose={() => {
            setMessageThreadOpen(false)
            setSelectedMessageUser(null)
            // Refresh correspondence when closing to update read status
            if (isOpen) {
              fetchCorrespondence()
            }
          }}
          otherUserId={selectedMessageUser.id}
          otherUserName={selectedMessageUser.name}
          otherUserAvatar={selectedMessageUser.avatar}
          currentUserId={userId}
        />
      )}
    </>
  )
}
